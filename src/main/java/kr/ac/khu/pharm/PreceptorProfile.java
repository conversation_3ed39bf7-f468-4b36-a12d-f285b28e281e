package kr.ac.khu.pharm;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import kr.ac.khu.pharm.main.user.entity.UserProfile;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(name = "tc_preceptor_profile")
public class PreceptorProfile extends UserProfile {
    @Id
    @Column(name = "profile_sn", nullable = false)
    private Long profileSn;

    @NotNull
    @Column(name = "user_sn", nullable = false)
    private Integer userSn;

    @Size(max = 100)
    @Column(name = "institution_name", length = 100)
    private String institutionName;

    @Size(max = 20)
    @Column(name = "internship_type", length = 20)
    private String internshipType;

    @Size(max = 100)
    @Column(name = "institution_cat", length = 100)
    private String institutionCat;

    @Size(max = 20)
    @Column(name = "internship_phase", length = 20)
    private String internshipPhase;

    @Size(max = 100)
    @Column(name = "schedule_sn", length = 100)
    private String scheduleSn;

    @Column(name = "approval_sttus")
    private Character approvalSttus;

    @Size(max = 100)
    @Column(name = "mngr_nm", length = 100)
    private String mngrNm;

    @Size(max = 32)
    @Column(name = "phone", length = 32)
    private String phone;

    @Size(max = 32)
    @Column(name = "license_no", length = 32)
    private String licenseNo;

    @Size(max = 64)
    @Column(name = "email", length = 64)
    private String email;

    @Size(max = 256)
    @Column(name = "admin_memo", length = 256)
    private String adminMemo;

    @Size(max = 10)
    @Column(name = "doc_mng_sn", length = 10)
    private String docMngSn;

    @Column(name = "reg_date")
    private Instant regDate;

    @Column(name = "mod_date")
    private Instant modDate;

    @Size(max = 32)
    @Column(name = "reg_user_id", length = 32)
    private String regUserId;

    @Size(max = 64)
    @Column(name = "reg_user_nm", length = 64)
    private String regUserNm;

    @Size(max = 100)
    @Column(name = "mod_user_id", length = 100)
    private String modUserId;

}