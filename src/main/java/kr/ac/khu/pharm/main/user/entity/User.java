package kr.ac.khu.pharm.main.user.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import kr.ac.khu.pharm.main.common.entity.BaseModel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;

@Getter
@Setter
@Entity
@Table(name = "tm_user_mng")
public class User extends BaseModel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "USER_SN", nullable = false)
    private Integer id;

    @Size(max = 64)
    @Column(name = "USER_ID", length = 64)
    private String userId;

    @Column(name = "USER_ORGNZT")
    private Integer userOrgnzt;

    @Size(max = 64)
    @Column(name = "PASSWORD", length = 64)
    private String password;

    @Size(max = 64)
    @Column(name = "USER_NM", length = 64)
    private String userNm;

    @Size(max = 64)
    @Column(name = "MAIL", length = 64)
    private String mail;

    @Size(max = 32)
    @Column(name = "MOBILE", length = 32)
    private String mobile;

    @Size(max = 32)
    @Column(name = "PHONE", length = 32)
    private String phone;

    @Size(max = 6)
    @Column(name = "ZIPCODE", length = 6)
    private String zipcode;

    @Size(max = 512)
    @Column(name = "ADDRESS1", length = 512)
    private String address1;

    @Size(max = 512)
    @Column(name = "ADDRESS2", length = 512)
    private String address2;

    @Size(max = 1)
    @Column(name = "STTUS", length = 1)
    private String sttus;


    @Size(max = 64)
    @Column(name = "MOD_USER_ID", length = 64)
    private String modUserId;

    @Size(max = 1)
    @ColumnDefault("0")
    @Column(name = "DELETE_CD", length = 1)
    private String deleteCd;

    @Size(max = 50)
    @Column(name = "SITE_ID", length = 50)
    private String siteId;

    @Size(max = 32)
    @Column(name = "MEM_NUM", length = 32)
    private String memNum;

    @Size(max = 4)
    @Column(name = "INTERN_YEAR", length = 4)
    private String internYear;

    @Size(max = 1024)
    @Column(name = "ADMIN_MEMO", length = 1024)
    private String adminMemo;

    @Size(max = 200)
    @Column(name = "org_name", length = 200)
    private String orgName;

    @Size(max = 10)
    @Column(name = "intern_type", length = 10)
    private String internType;

    @Size(max = 10)
    @Column(name = "internship_level", length = 10)
    private String internshipLevel;

    @Size(max = 64)
    @Column(name = "EMP_NO", length = 64)
    private String empNo;

    @Size(max = 16)
    @Column(name = "APPROVAL_STATUS", length = 16)
    private String approvalStatus;

    @Size(max = 256)
    @Column(name = "DOC_MNG", length = 256)
    private String docMng;

    @Size(max = 256)
    @Column(name = "INTERN_ORG_NM", length = 256)
    private String internOrgNm;

    @Size(max = 64)
    @Column(name = "INTERN_ORG_TYPE", length = 64)
    private String internOrgType;

    @Size(max = 16)
    @Column(name = "INTERN_ROUND", length = 16)
    private String internRound;

    @Size(max = 64)
    @Column(name = "CHARGE_PERSON", length = 64)
    private String chargePerson;

    @Size(max = 32)
    @Column(name = "CHARGE_PERSON_PHONE", length = 32)
    private String chargePersonPhone;

    @Size(max = 64)
    @Column(name = "LICENSE_NO", length = 64)
    private String licenseNo;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "USER_GBN_CD")
    private Role role;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "profile_id")
    private UserProfile profile;

    @Column(name = "FAILED_ATTEMPTS")
    private int failedAttempts;

    @Column(name = "is_locked")
    private boolean isLocked;

    @Column(name = "is_verified")
    private boolean isVerified;

    public boolean getIsAccountNonLocked() {
        return !isLocked;
    }

}