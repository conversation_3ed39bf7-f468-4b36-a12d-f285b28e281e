package kr.ac.khu.pharm.main.practiceScheduleMng.repository;

import kr.ac.khu.pharm.main.practiceScheduleMng.entity.PracticeSchedule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PracticeScheduleRepository extends JpaRepository<PracticeSchedule, Integer> {

//    @Query("SELECT COUNT(s) FROM PracticeSchedule s WHERE s.userId = :userId AND s.delAt = 'N'")
//    long countActiveSchedulesByUserId(@Param("userId") String userId);

    @Query("SELECT s FROM PracticeSchedule s WHERE s.userId = :userId AND s.delAt = 'N' ORDER BY s.regDate DESC")
    List<PracticeSchedule> findActiveSchedulesByUserId(@Param("userId") String userId);

    Optional<PracticeSchedule> findBySpIdAndUserIdAndDelAt(Integer spId, String userId, String delAt);
}
