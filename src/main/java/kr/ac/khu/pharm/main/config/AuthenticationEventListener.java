package kr.ac.khu.pharm.main.config;

import kr.ac.khu.pharm.main.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.security.authentication.event.AbstractAuthenticationFailureEvent;
import org.springframework.security.authentication.event.AuthenticationSuccessEvent;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class AuthenticationEventListener {

    private final UserService userService;

    @EventListener
    public void onAuthenticationSuccess(AuthenticationSuccessEvent event) {

        String username = event.getAuthentication().getName();
        log.debug("AuthenticationSuccessEvent called {}", username);
        userService.handleAuthenticationSuccess(username);
    }

    @EventListener
    public void onAuthenticationFailure(AbstractAuthenticationFailureEvent event) {
        String username = event.getAuthentication().getName();
        log.debug("onAuthenticationFailure called {}", username);
        userService.handleAuthenticationFailure(username);
    }
}
