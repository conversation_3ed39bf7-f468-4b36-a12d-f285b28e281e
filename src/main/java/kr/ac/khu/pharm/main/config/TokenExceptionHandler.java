package kr.ac.khu.pharm.main.config;

import kr.ac.khu.pharm.main.auth.TokenRefreshException;
import kr.ac.khu.pharm.main.auth.TokenResetException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.HashMap;
import java.util.Map;

@ControllerAdvice
public class TokenExceptionHandler {

    @ExceptionHandler({TokenRefreshException.class, TokenResetException.class})
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ResponseEntity<Object> handleTokenRefreshException(RuntimeException e) {
        Map<String, Object> errors = new HashMap<>();
        errors.put("error", e.getMessage());
        return new ResponseEntity<>(errors, HttpStatus.FORBIDDEN);
    }
}
