package kr.ac.khu.pharm.main.user.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
public abstract class UserProfile {
    @Id
    @GeneratedValue(strategy = GenerationType.TABLE)
    protected Long profileSn;

    @OneToOne(mappedBy = "profile", optional = false)
    private User user;

}
