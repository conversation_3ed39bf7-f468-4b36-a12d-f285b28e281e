package kr.ac.khu.pharm.main.config;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.*;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json");

        if (authException instanceof BadCredentialsException) {
            response.getWriter().write("{\"error\":\"Invalid username or password\"}");
        } else if (authException instanceof DisabledException) {
            response.getWriter().write("{\"error\":\"Your account is disabled\"}");
        } else if (authException instanceof AccountExpiredException) {
            response.getWriter().write("{\"error\":\"Your account has expired\"}");
        } else if (authException instanceof LockedException) {
            log.debug(authException.getMessage());
            response.getWriter().write("{\"error\":\"Your account has been locked\"}");
        } else if (authException instanceof InsufficientAuthenticationException) {
            response.getWriter().write("{\"error\":\"Your account has insufficient privileges\"}");
        }

    }
}

