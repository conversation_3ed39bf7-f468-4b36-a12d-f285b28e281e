package kr.ac.khu.pharm.main.practiceScheduleMng.service;

import jakarta.validation.Valid;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.insert.HolidayInsertDto;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.insert.ScheduleMngInsertRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.insert.ScheduleMngInsertResponse;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.list.HolidayDto;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.list.ScheduleMngListRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.list.ScheduleMngListResponse;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.update.HolidayUpdateDto;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.update.ScheduleMngUpdateRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.update.ScheduleMngUpdateResponse;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.view.ScheduleMngViewResponse;
import kr.ac.khu.pharm.main.practiceScheduleMng.entity.PracticeHolidays;
import kr.ac.khu.pharm.main.practiceScheduleMng.entity.PracticeSchedule;
import kr.ac.khu.pharm.main.practiceScheduleMng.repository.PracticeHolidaysRepository;
import kr.ac.khu.pharm.main.practiceScheduleMng.repository.PracticeScheduleMngRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class PracticeScheduleMngService {
    
    private final PracticeScheduleMngRepository practiceScheduleMngRepository;
    private final PracticeHolidaysRepository practiceHolidaysRepository;

    /**
     * Get list of practice schedules for a user
     */
    public List<ScheduleMngListResponse> list(@Valid ScheduleMngListRequest request) {
        log.info("Fetching practice schedules for userId: {}", request.getUserId());
        
        List<PracticeSchedule> practiceSchedules = practiceScheduleMngRepository.findAllByUserIdAndDelAt(request.getUserId(), "N");
        
        log.info("Found {} practice schedules for userId: {}", practiceSchedules.size(), request.getUserId());
        
        return practiceSchedules.stream().map(this::mapToListResponse).collect(Collectors.toList());
    }

    /**
     * Get a single practice schedule by ID
     */
    public ScheduleMngViewResponse findBySpId(Integer spId) {
        log.info("Fetching practice schedule with spId: {}", spId);
        
        Optional<PracticeSchedule> practiceScheduleOptional = practiceScheduleMngRepository.findBySpIdAndDelAt(spId, "N");
        
        if (practiceScheduleOptional.isEmpty()) {
            log.warn("Practice schedule not found with spId: {}", spId);
            throw new RuntimeException("Practice schedule not found with ID: " + spId);
        }
        
        PracticeSchedule practiceSchedule = practiceScheduleOptional.get();
        return mapToViewResponse(practiceSchedule);
    }

    /**
     * Create a new practice schedule
     */
    @Transactional
    public ScheduleMngInsertResponse createPracticeSchedule(@Valid ScheduleMngInsertRequest request) {
        log.info("Creating new practice schedule for userId: {}", request.getUserId());
        
        // Validate date range
        if (request.getStartDate().isAfter(request.getEndDate())) {
            throw new RuntimeException("Start date must be before end date");
        }
        
        // Create practice schedule entity
        PracticeSchedule practiceSchedule = new PracticeSchedule();
        practiceSchedule.setUserSn(request.getUserSn());
        practiceSchedule.setUserId(request.getUserId());
        practiceSchedule.setTeam(request.getTeam());
        practiceSchedule.setDegree(request.getDegree());
        practiceSchedule.setStartDate(request.getStartDate());
        practiceSchedule.setEndDate(request.getEndDate());
        practiceSchedule.setDaysOfWeek(request.getDaysOfWeek());
        practiceSchedule.setDelAt("N");
        practiceSchedule.setRegDate(Instant.now());
        practiceSchedule.setRegUserId(request.getUserId());
        
        // Save practice schedule
        PracticeSchedule savedSchedule = practiceScheduleMngRepository.save(practiceSchedule);
        
        // Create holidays if provided
        if (request.getHolidays() != null && !request.getHolidays().isEmpty()) {
            createHolidays(savedSchedule.getSpId(), request.getHolidays());
        }
        
        log.info("Successfully created practice schedule with spId: {}", savedSchedule.getSpId());
        
        return mapToInsertResponse(savedSchedule);
    }

    /**
     * Update an existing practice schedule
     */
    @Transactional
    public ScheduleMngUpdateResponse updatePracticeSchedule(Integer spId, @Valid ScheduleMngUpdateRequest request) {
        log.info("Updating practice schedule with spId: {}", spId);

        // Find existing schedule
        Optional<PracticeSchedule> practiceScheduleOptional = practiceScheduleMngRepository.findBySpIdAndDelAt(spId, "N");

        if (practiceScheduleOptional.isEmpty()) {
            log.warn("Practice schedule not found with spId: {}", spId);
            throw new RuntimeException("Practice schedule not found with ID: " + spId);
        }

        PracticeSchedule practiceSchedule = practiceScheduleOptional.get();

        // Update fields if provided
        if (request.getTeam() != null) {
            practiceSchedule.setTeam(request.getTeam());
        }

        if (request.getDegree() != null) {
            practiceSchedule.setDegree(request.getDegree());
        }

        if (request.getStartDate() != null) {
            practiceSchedule.setStartDate(request.getStartDate());
        }

        if (request.getEndDate() != null) {
            practiceSchedule.setEndDate(request.getEndDate());
        }

        // Validate date range
        if (practiceSchedule.getStartDate().isAfter(practiceSchedule.getEndDate())) {
            throw new RuntimeException("Start date must be before end date");
        }

        if (request.getDaysOfWeek() != null) {
            practiceSchedule.setDaysOfWeek(request.getDaysOfWeek());
        }

        // Update audit fields
        practiceSchedule.setModDate(Instant.now());
        practiceSchedule.setModUserId(request.getCurrentUserId());

        // Save updated schedule
        PracticeSchedule updatedSchedule = practiceScheduleMngRepository.save(practiceSchedule);

        // Update holidays
        updateHolidays(spId, request);

        log.info("Successfully updated practice schedule with spId: {}", spId);

        return mapToUpdateResponse(updatedSchedule);
    }

    /**
     * Soft delete a practice schedule by setting delAt to 'Y'
     */
    @Transactional
    public void deletePracticeScheduleById(Integer spId, String currentUserId) {
        log.info("Attempting to delete practice schedule with spId: {} by user: {}", spId, currentUserId);

        // Find active practice schedule (delAt = 'N')
        Optional<PracticeSchedule> practiceScheduleOptional = practiceScheduleMngRepository.findBySpIdAndDelAt(spId, "N");

        if (practiceScheduleOptional.isEmpty()) {
            log.warn("Active practice schedule not found with spId: {}", spId);
            throw new RuntimeException("Active practice schedule not found with ID: " + spId);
        }

        PracticeSchedule practiceSchedule = practiceScheduleOptional.get();

        // Perform soft delete
        practiceSchedule.setDelAt("Y");
        practiceSchedule.setModDate(Instant.now());
        practiceSchedule.setModUserId(currentUserId);

        practiceScheduleMngRepository.save(practiceSchedule);

        log.info("Successfully deleted practice schedule with spId: {} by user: {}", spId, currentUserId);
    }

    // ==================== HELPER METHODS ====================

    /**
     * Create holidays for a practice schedule
     */
    private void createHolidays(Integer spId, List<HolidayInsertDto> holidayDtos) {
        for (HolidayInsertDto holidayDto : holidayDtos) {
            PracticeHolidays holiday = new PracticeHolidays();

            // Set the practice schedule reference
            PracticeSchedule practiceSchedule = new PracticeSchedule();
            practiceSchedule.setSpId(spId);
            holiday.setSpId(practiceSchedule);

            holiday.setHolidayDate(holidayDto.getHolidayDate());
            holiday.setHolidayReason(holidayDto.getHolidayReason());

            practiceHolidaysRepository.save(holiday);
        }

        log.debug("Created {} holidays for spId: {}", holidayDtos.size(), spId);
    }

    /**
     * Update holidays for a practice schedule
     */
    private void updateHolidays(Integer spId, ScheduleMngUpdateRequest request) {
        // Delete holidays if specified
        if (request.getHolidaysToDelete() != null && !request.getHolidaysToDelete().isEmpty()) {
            for (Integer holidayId : request.getHolidaysToDelete()) {
                practiceHolidaysRepository.deleteById(holidayId);
            }
            log.debug("Deleted {} holidays for spId: {}", request.getHolidaysToDelete().size(), spId);
        }

        // Add/update holidays if specified
        if (request.getHolidays() != null && !request.getHolidays().isEmpty()) {
            for (HolidayUpdateDto holidayDto : request.getHolidays()) {
                if (holidayDto.getHolidayId() == null) {
                    // Create new holiday
                    PracticeHolidays holiday = new PracticeHolidays();

                    PracticeSchedule practiceSchedule = new PracticeSchedule();
                    practiceSchedule.setSpId(spId);
                    holiday.setSpId(practiceSchedule);

                    holiday.setHolidayDate(holidayDto.getHolidayDate());
                    holiday.setHolidayReason(holidayDto.getHolidayReason());

                    practiceHolidaysRepository.save(holiday);
                } else {
                    // Update existing holiday
                    Optional<PracticeHolidays> existingHoliday = practiceHolidaysRepository.findById(holidayDto.getHolidayId());
                    if (existingHoliday.isPresent()) {
                        PracticeHolidays holiday = existingHoliday.get();
                        holiday.setHolidayDate(holidayDto.getHolidayDate());
                        holiday.setHolidayReason(holidayDto.getHolidayReason());
                        practiceHolidaysRepository.save(holiday);
                    }
                }
            }
            log.debug("Updated {} holidays for spId: {}", request.getHolidays().size(), spId);
        }
    }

    // ==================== MAPPING METHODS ====================

    /**
     * Maps a PracticeSchedule entity to a ScheduleMngListResponse DTO
     */
    private ScheduleMngListResponse mapToListResponse(PracticeSchedule practiceSchedule) {
        ScheduleMngListResponse response = new ScheduleMngListResponse();
        mapCommonFields(practiceSchedule, response);

        List<HolidayDto> holidayDtoList = fetchAndMapHolidays(practiceSchedule.getSpId());
        response.setHoliday(holidayDtoList);

        return response;
    }

    /**
     * Maps a PracticeSchedule entity to a ScheduleMngViewResponse DTO
     */
    private ScheduleMngViewResponse mapToViewResponse(PracticeSchedule practiceSchedule) {
        ScheduleMngViewResponse response = new ScheduleMngViewResponse();
        mapCommonFields(practiceSchedule, response);

        List<HolidayDto> holidayDtoList = fetchAndMapHolidays(practiceSchedule.getSpId());
        response.setHoliday(holidayDtoList);

        return response;
    }

    /**
     * Maps a PracticeSchedule entity to a ScheduleMngInsertResponse DTO
     */
    private ScheduleMngInsertResponse mapToInsertResponse(PracticeSchedule practiceSchedule) {
        ScheduleMngInsertResponse response = new ScheduleMngInsertResponse();
        response.setSpId(practiceSchedule.getSpId());
        response.setUserSn(practiceSchedule.getUserSn());
        response.setUserId(practiceSchedule.getUserId());
        response.setTeam(practiceSchedule.getTeam());
        response.setDegree(practiceSchedule.getDegree());
        response.setStartDate(practiceSchedule.getStartDate());
        response.setEndDate(practiceSchedule.getEndDate());
        response.setDaysOfWeek(practiceSchedule.getDaysOfWeek());
        response.setMessage("Practice schedule created successfully");

        return response;
    }

    /**
     * Maps a PracticeSchedule entity to a ScheduleMngUpdateResponse DTO
     */
    private ScheduleMngUpdateResponse mapToUpdateResponse(PracticeSchedule practiceSchedule) {
        ScheduleMngUpdateResponse response = new ScheduleMngUpdateResponse();
        response.setSpId(practiceSchedule.getSpId());
        response.setTeam(practiceSchedule.getTeam());
        response.setDegree(practiceSchedule.getDegree());
        response.setStartDate(practiceSchedule.getStartDate());
        response.setEndDate(practiceSchedule.getEndDate());
        response.setDaysOfWeek(practiceSchedule.getDaysOfWeek());
        response.setMessage("Practice schedule updated successfully");

        return response;
    }

    /**
     * Maps common fields from PracticeSchedule entity to ScheduleMngListResponse
     */
    private void mapCommonFields(PracticeSchedule practiceSchedule, ScheduleMngListResponse response) {
        response.setSpId(practiceSchedule.getSpId());
        response.setUserSn(practiceSchedule.getUserSn());
        response.setUserId(practiceSchedule.getUserId());
        response.setTeam(practiceSchedule.getTeam());
        response.setDegree(practiceSchedule.getDegree());
        response.setStartDate(practiceSchedule.getStartDate());
        response.setEndDate(practiceSchedule.getEndDate());
        response.setDaysOfWeek(practiceSchedule.getDaysOfWeek());
        response.setDelAt(practiceSchedule.getDelAt());
        response.setRegUserId(practiceSchedule.getRegUserId());
    }

    /**
     * Maps common fields from PracticeSchedule entity to ScheduleMngViewResponse
     */
    private void mapCommonFields(PracticeSchedule practiceSchedule, ScheduleMngViewResponse response) {
        response.setSpId(practiceSchedule.getSpId());
        response.setUserSn(practiceSchedule.getUserSn());
        response.setUserId(practiceSchedule.getUserId());
        response.setTeam(practiceSchedule.getTeam());
        response.setDegree(practiceSchedule.getDegree());
        response.setStartDate(practiceSchedule.getStartDate());
        response.setEndDate(practiceSchedule.getEndDate());
        response.setDaysOfWeek(practiceSchedule.getDaysOfWeek());
        response.setDelAt(practiceSchedule.getDelAt());
        response.setRegUserId(practiceSchedule.getRegUserId());
    }

    /**
     * Fetches holidays for a given spId and maps them to HolidayDto list
     */
    private List<HolidayDto> fetchAndMapHolidays(Integer spId) {
        List<PracticeHolidays> holidays = practiceHolidaysRepository.findBySpIdSpId(spId);
        log.debug("Fetching holidays for spId: {}, found {} holidays", spId, holidays.size());

        if (holidays.isEmpty()) {
            log.debug("No holidays found for spId: {}", spId);
            return new ArrayList<>();
        }

        List<HolidayDto> holidayDtoList = mapHolidaysToDtoList(holidays, spId);
        log.debug("Successfully mapped {} holidays for spId: {}", holidayDtoList.size(), spId);

        return holidayDtoList;
    }

    /**
     * Maps a list of PracticeHolidays entities to a list of HolidayDto objects
     */
    private List<HolidayDto> mapHolidaysToDtoList(List<PracticeHolidays> holidays, Integer spId) {
        return holidays.stream()
                .map(holiday -> mapToHolidayDto(holiday, spId))
                .collect(Collectors.toList());
    }

    /**
     * Maps a single PracticeHolidays entity to HolidayDto
     */
    private HolidayDto mapToHolidayDto(PracticeHolidays holiday, Integer spId) {
        HolidayDto holidayDto = new HolidayDto();
        holidayDto.setHolidayId(holiday.getHolidayId());
        holidayDto.setHolidayReason(holiday.getHolidayReason());
        holidayDto.setSpId(spId);
        holidayDto.setHolidayDate(holiday.getHolidayDate());
        return holidayDto;
    }
}
