package kr.ac.khu.pharm.main.practiceScheduleMng.exception;

public class PracticeScheduleAccessDeniedException extends RuntimeException {
    
    public PracticeScheduleAccessDeniedException(String message) {
        super(message);
    }
    
    public PracticeScheduleAccessDeniedException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public PracticeScheduleAccessDeniedException(Integer spId, String userId) {
        super("Access denied to practice schedule with ID: " + spId + " for user: " + userId);
    }
}
