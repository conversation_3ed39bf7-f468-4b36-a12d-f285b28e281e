package kr.ac.khu.pharm.main.practiceScheduleMng.entity;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import kr.ac.khu.pharm.main.common.entity.BaseModel;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "tc_student_practice_schedule")
public class PracticeSchedule extends BaseModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SP_ID", nullable = false)
    private Integer spId;

    @NotNull
    @Column(name = "USER_SN", nullable = false)
    private Integer userSn;

    @NotNull
    @Size(max = 64)
    @Column(name = "USER_ID", nullable = false, length = 64)
    private String userId;

    @NotNull
    @Column(name = "TEAM", nullable = false, length = 1)
    private String team;

    @NotNull
    @Column(name = "DEGREE", nullable = false, length = 1)
    private Integer degree;

    @NotNull
    @Column(name = "START_DATE", nullable = false)
    private LocalDate startDate;

    @NotNull
    @Column(name = "END_DATE", nullable = false)
    private LocalDate endDate;

    @NotNull
    @Size(max = 20)
    @Pattern(regexp = "^[1-7](,[1-7])*$", message = "Days of week must be comma-separated numbers (1-7)")
    @Column(name = "DAYS_OF_WEEK", nullable = false, length = 14)
    private String daysOfWeek;

    @NotNull
    @Size(max = 1)
    @Column(name = "DEL_AT", nullable = false, length = 1)
    private String delAt = "N";

    @OneToMany(mappedBy = "practiceSchedule", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<PracticeHolidays> holidays;
}
