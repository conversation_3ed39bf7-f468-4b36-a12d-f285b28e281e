package kr.ac.khu.pharm.main.practiceScheduleMng.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(name = "tc_practice_holidays")
public class PracticeHolidays {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "HOLIDAY_ID", nullable = false)
    private Integer holidayId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SP_ID", nullable = false)
    @JsonBackReference
    private PracticeSchedule practiceSchedule;
    
    @NotNull
    @Column(name = "HOLIDAY_DATE", nullable = false)
    private LocalDate holidayDate;
    
    @Size(max = 128)
    @Column(name = "HOLIDAY_REASON", length = 128)
    private String holidayReason;
}
