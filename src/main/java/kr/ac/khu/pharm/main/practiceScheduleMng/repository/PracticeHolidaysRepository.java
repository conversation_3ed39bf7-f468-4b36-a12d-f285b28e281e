package kr.ac.khu.pharm.main.practiceScheduleMng.repository;

import kr.ac.khu.pharm.main.practiceScheduleMng.entity.PracticeHolidays;
import kr.ac.khu.pharm.main.practiceScheduleMng.entity.PracticeSchedule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PracticeHolidaysRepository extends JpaRepository<PracticeHolidays, Integer> {

    List<PracticeHolidays> findByPracticeScheduleSpId(Integer spId);
    @Modifying
    @Query("DELETE FROM PracticeHolidays h WHERE h.practiceSchedule.spId = :spId")
    void deleteByPracticeScheduleSpId(@Param("spId") Integer spId);

}
