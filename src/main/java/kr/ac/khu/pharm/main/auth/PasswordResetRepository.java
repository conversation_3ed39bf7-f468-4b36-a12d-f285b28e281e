package kr.ac.khu.pharm.main.auth;

import kr.ac.khu.pharm.main.auth.entity.PasswordResetEntity;
import kr.ac.khu.pharm.main.user.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface PasswordResetRepository extends JpaRepository<PasswordResetEntity, Long> {
    void deleteByUser(User user);


    Optional<PasswordResetEntity> findByToken(String token);

    List<PasswordResetEntity> findByUserAndRegDateAfter(User user, Instant after);

    @Modifying
    @Query("update PasswordResetEntity set isActive = false where user = ?1")
    void updateIsActive(User user);

}
