package kr.ac.khu.pharm.main.user.repository;

import kr.ac.khu.pharm.main.user.entity.Role;
import kr.ac.khu.pharm.main.user.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    User findByUserId(String username);
    Optional<User> findByUserIdAndRoleAndOrgNameAndIsVerified(String username, Role role, String orgName, boolean isVerified);
}
