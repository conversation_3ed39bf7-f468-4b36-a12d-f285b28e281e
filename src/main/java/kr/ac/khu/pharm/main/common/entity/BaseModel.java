package kr.ac.khu.pharm.main.common.entity;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.Instant;

@MappedSuperclass
@Data
public class BaseModel {
    @Column(name = "REG_DATE")
    private Instant regDate;

    @Column(name = "MOD_DATE")
    private Instant modDate;

    @Size(max = 64)
    @Column(name = "REG_USER_ID", length = 64)
    private String regUserId;

    @Size(max = 64)
    @Column(name = "MOD_USER_ID", length = 64)
    private String modUserId;

}
