package kr.ac.khu.pharm.main.practiceScheduleMng.exception;

public class PracticeScheduleNotFoundException extends RuntimeException {
    
    public PracticeScheduleNotFoundException(String message) {
        super(message);
    }
    
    public PracticeScheduleNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public PracticeScheduleNotFoundException(Integer spId) {
        super("Practice schedule not found with ID: " + spId);
    }
    
    public PracticeScheduleNotFoundException(Integer spId, String userId) {
        super("Practice schedule not found with ID: " + spId + " for user: " + userId);
    }
}
