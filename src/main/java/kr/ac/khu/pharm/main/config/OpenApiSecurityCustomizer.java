package kr.ac.khu.pharm.main.config;

import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.PathItem;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.AntPathMatcher;

import java.util.Collections;
import java.util.List;

@Configuration
public class OpenApiSecurityCustomizer {

    private final PublicApiPaths publicApiPaths;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    public OpenApiSecurityCustomizer(PublicApiPaths publicApiPaths) {
        this.publicApiPaths = publicApiPaths;
    }

    @Bean
    public OpenApiCustomizer publicEndpointsCustomizer() {
        return openApi -> {
            List<String> whitelist = publicApiPaths.getAllWhitelistedPaths();

            openApi.getPaths().forEach((path, pathItem) -> {
                if (isWhitelisted(path, whitelist)) {
                    removeSecurity(pathItem);
                }
            });
        };
    }

    private boolean isWhitelisted(String path, List<String> whitelist) {
        return whitelist.stream().anyMatch(pattern -> pathMatcher.match(pattern, path));
    }

    private void removeSecurity(PathItem pathItem) {
        for (Operation operation : pathItem.readOperations()) {
            operation.setSecurity(Collections.emptyList());
        }
    }
}
