package kr.ac.khu.pharm.main.auth.service;

import jakarta.transaction.Transactional;
import kr.ac.khu.pharm.main.auth.TokenRefreshException;
import kr.ac.khu.pharm.main.auth.entity.RefreshToken;
import kr.ac.khu.pharm.main.auth.repository.RefreshTokenRepository;
import kr.ac.khu.pharm.main.user.entity.User;
import kr.ac.khu.pharm.main.user.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class RefreshTokenService {

    private final UserRepository userRepository;

    private final RefreshTokenRepository refreshTokenRepository;

    @Value("${application.security.jwt.refresh-token-expiration}")
    private int refreshTokenExpiration;

    public Optional<RefreshToken> findByRefreshToken(String refreshToken) {
        return refreshTokenRepository.findByRefreshToken(refreshToken);
    }

    @Transactional
    public RefreshToken createRefreshToken(Long userSn) {
        User user = userRepository.findById(userSn).orElseThrow(() -> new UsernameNotFoundException("User not found!"));
        refreshTokenRepository.deleteByUser(user);
        RefreshToken refreshToken = RefreshToken.builder()
                .user(user)
                .expiryDate(Instant.now().plusMillis(refreshTokenExpiration))
                .refreshToken(UUID.randomUUID().toString())
                .build();
        return refreshTokenRepository.save(refreshToken);
    }

    public RefreshToken verifyRefreshTokenExpiration(RefreshToken refreshToken) {
        if (refreshToken.getExpiryDate().isBefore(Instant.now())) {
            refreshTokenRepository.delete(refreshToken);
            throw new TokenRefreshException(refreshToken.getRefreshToken(), "Refresh token expired!");
        }
        return refreshToken;
    }

    @Transactional
    public void deleteRefreshTokenByUserSn(Long userSn) {
        User user = userRepository.findById(userSn).orElseThrow(() -> new UsernameNotFoundException("User not found!"));
        refreshTokenRepository.deleteByUser(user);
    }

    @Transactional
    public void deleteExpiredRefreshTokens() {
        refreshTokenRepository.deleteExpiredTokens();
    }
}
