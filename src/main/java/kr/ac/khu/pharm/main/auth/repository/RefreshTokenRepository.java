package kr.ac.khu.pharm.main.auth.repository;

import kr.ac.khu.pharm.main.auth.entity.RefreshToken;
import kr.ac.khu.pharm.main.user.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface RefreshTokenRepository extends JpaRepository<RefreshToken, Long> {

    Optional<RefreshToken> findByRefreshToken(String refreshToken);

    @Modifying
    @Query("delete from RefreshToken rt where rt.user = ?1")
    void deleteByUser(User user);

    @Modifying
    @Query("DELETE FROM RefreshToken WHERE expiryDate < CURRENT_TIMESTAMP ")
    void deleteExpiredTokens();

}
