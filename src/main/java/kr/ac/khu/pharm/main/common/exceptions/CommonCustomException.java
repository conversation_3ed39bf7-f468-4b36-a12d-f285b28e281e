package kr.ac.khu.pharm.main.common.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;


public class CommonCustomException extends RuntimeException {

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public class DataBaseDisconnecteException extends RuntimeException {
        public DataBaseDisconnecteException() {
            super("DataBase Disconnected");
        }
    }
}



