package kr.ac.khu.pharm.main.auth;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.FORBIDDEN)
public class TokenRefreshException extends RuntimeException {
    public TokenRefreshException(String requestRefreshToken, String message) {
        super(String.format("Invalid refresh token: [%s]: %s", requestRefreshToken, message));
    }
}
