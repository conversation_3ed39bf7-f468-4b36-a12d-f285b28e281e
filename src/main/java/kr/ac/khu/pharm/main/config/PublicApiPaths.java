package kr.ac.khu.pharm.main.config;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

@Component
public class PublicApiPaths {
    public static final String[] AUTH_WHITELIST = {
            "/auth/login",
//            "/auth/register", // disabled registration.
            "/auth/refresh",
            "/auth/forgot-password",
            "/auth/reset-password/**",
    };

    public static final String[] SWAGGER = {
            "/swagger-ui/**",
            "/v3/api-docs/**",
    };

    public static final String[] WHITELIST = {
//            "/user/search",  // todo reimplement proper search
    };

    public List<String> getAllWhitelistedPaths() {
        return Stream.of(AUTH_WHITELIST, SWAGGER, WHITELIST).flatMap(Arrays::stream).toList();
    }
}
