package kr.ac.khu.pharm.main.user.service;

import kr.ac.khu.pharm.main.user.entity.Role;
import kr.ac.khu.pharm.main.user.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class RoleService {
    private final RoleRepository roleRepository;

    public List<Role> findAll() {
        return roleRepository.findAll();
    }

    public Role findByRoleName(String roleName) {
        return roleRepository.findByName(roleName);
    }

}
