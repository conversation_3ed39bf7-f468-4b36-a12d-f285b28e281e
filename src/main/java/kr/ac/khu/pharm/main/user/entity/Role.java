package kr.ac.khu.pharm.main.user.entity;


import jakarta.persistence.*;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Entity
@Data
@Table(name = "tm_roles", uniqueConstraints = {@UniqueConstraint(columnNames = "name", name = "UK_role")})
public class Role {
    @Id
    private String id;
    @Column(unique = true, nullable = false)
    private String name;

    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<User> users = new ArrayList<>();
}

