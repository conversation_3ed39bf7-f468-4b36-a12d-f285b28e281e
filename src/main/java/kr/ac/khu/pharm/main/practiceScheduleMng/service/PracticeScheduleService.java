package kr.ac.khu.pharm.main.practiceScheduleMng.service;

import jakarta.validation.Valid;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.HolidayRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.PracticeScheduleInsertRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.PracticeScheduleUpdateRequest;
import kr.ac.khu.pharm.main.practiceScheduleMng.entity.PracticeHolidays;
import kr.ac.khu.pharm.main.practiceScheduleMng.entity.PracticeSchedule;
import kr.ac.khu.pharm.main.practiceScheduleMng.repository.PracticeHolidaysRepository;
import kr.ac.khu.pharm.main.practiceScheduleMng.repository.PracticeScheduleRepository;
import kr.ac.khu.pharm.main.practiceScheduleMng.exception.PracticeScheduleNotFoundException;
import kr.ac.khu.pharm.main.practiceScheduleMng.exception.PracticeScheduleValidationException;
import kr.ac.khu.pharm.main.common.util.CommonUtil;
import kr.ac.khu.pharm.main.user.entity.User;
import kr.ac.khu.pharm.main.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class PracticeScheduleService {

    private final PracticeScheduleRepository practiceScheduleRepository;
    private final PracticeHolidaysRepository practiceHolidaysRepository;
    private final UserService userService;

    public List<PracticeSchedule> list() {
        String currentUserId = CommonUtil.getCurrentUserId();
        List<PracticeSchedule> schedules = practiceScheduleRepository.findActiveSchedulesByUserId(currentUserId);
        return schedules;
    }

    public PracticeSchedule findById(Integer spId) {
        String currentUserId = CommonUtil.getCurrentUserId();

        Optional<PracticeSchedule> scheduleOptional = practiceScheduleRepository.findBySpIdAndUserIdAndDelAt(spId, currentUserId, "N");

        if (scheduleOptional.isEmpty()) {
            throw new PracticeScheduleNotFoundException(spId, currentUserId);
        }

        PracticeSchedule schedule = scheduleOptional.get();
        return schedule;
    }

    @Transactional
    public PracticeSchedule insert(@Valid PracticeScheduleInsertRequest request) {
        String currentUserId = CommonUtil.getCurrentUserId();
        User currentUser = userService.getByUsername(currentUserId);

        if (request.getStartDate().isAfter(request.getEndDate())) {
            throw PracticeScheduleValidationException.invalidDateRange();
        }

        // Create practice schedule entity
        PracticeSchedule schedule = new PracticeSchedule();
        schedule.setUserSn(currentUser.getId());
        schedule.setUserId(currentUserId);
        schedule.setTeam(request.getTeam());
        schedule.setDegree(request.getDegree());
        schedule.setStartDate(request.getStartDate());
        schedule.setEndDate(request.getEndDate());
        schedule.setDaysOfWeek(request.getDaysOfWeek());
        schedule.setDelAt("N");
        schedule.setRegDate(Instant.now());
        schedule.setRegUserId(currentUserId);

        // Save practice schedule
        PracticeSchedule savedSchedule = practiceScheduleRepository.save(schedule);

        // Create holidays if provided
        if (request.getHolidays() != null && !request.getHolidays().isEmpty()) {
            createHolidays(savedSchedule.getSpId(), request.getHolidays());
        }

        return findById(savedSchedule.getSpId());
    }

    /**
     * Update an existing practice schedule
     */
    @Transactional
    public PracticeSchedule update(Integer spId, @Valid PracticeScheduleUpdateRequest request) {
        String currentUserId = CommonUtil.getCurrentUserId();

        Optional<PracticeSchedule> scheduleOptional = practiceScheduleRepository.findBySpIdAndUserIdAndDelAt(spId, currentUserId, "N");

        if (scheduleOptional.isEmpty()) {
            throw new PracticeScheduleNotFoundException(spId, currentUserId);
        }

        PracticeSchedule schedule = scheduleOptional.get();

        // Update fields if provided
        if (request.getTeam() != null) {
            schedule.setTeam(request.getTeam());
        }
        if (request.getDegree() != null) {
            schedule.setDegree(request.getDegree());
        }
        if (request.getStartDate() != null) {
            schedule.setStartDate(request.getStartDate());
        }
        if (request.getEndDate() != null) {
            schedule.setEndDate(request.getEndDate());
        }
        if (request.getDaysOfWeek() != null) {
            schedule.setDaysOfWeek(request.getDaysOfWeek());
        }

        // Validate date range if both dates are provided
        if (schedule.getStartDate().isAfter(schedule.getEndDate())) {
            throw PracticeScheduleValidationException.invalidDateRange();
        }

        schedule.setModDate(Instant.now());
        schedule.setModUserId(currentUserId);

        // Save updated schedule
        practiceScheduleRepository.save(schedule);

        // Handle holiday updates
        updateHolidays(spId, request);

        return findById(spId);
    }

    @Transactional
    public void delete(Integer spId) {
        String currentUserId = CommonUtil.getCurrentUserId();
        Optional<PracticeSchedule> scheduleOptional = practiceScheduleRepository.findBySpIdAndUserIdAndDelAt(spId, currentUserId, "N");

        if (scheduleOptional.isEmpty()) {
            throw new PracticeScheduleNotFoundException(spId, currentUserId);
        }

        PracticeSchedule schedule = scheduleOptional.get();
        schedule.setDelAt("Y");
        schedule.setModDate(Instant.now());
        schedule.setModUserId(currentUserId);

        practiceScheduleRepository.save(schedule);
    }

    public List<PracticeHolidays> holidayList(Integer spId) {
        return practiceHolidaysRepository.findByPracticeScheduleSpId(spId);
    }

    private void createHolidays(Integer spId, List<HolidayRequest> holidayRequests) {
        for (HolidayRequest holidayRequest : holidayRequests) {
            PracticeHolidays holiday = new PracticeHolidays();

            // Set the practice schedule reference
            PracticeSchedule practiceSchedule = new PracticeSchedule();
            practiceSchedule.setSpId(spId);
            holiday.setPracticeSchedule(practiceSchedule);

            holiday.setHolidayDate(holidayRequest.getHolidayDate());
            holiday.setHolidayReason(holidayRequest.getHolidayReason());

            practiceHolidaysRepository.save(holiday);
        }
    }

    private void updateHolidays(Integer spId, PracticeScheduleUpdateRequest request) {

        practiceHolidaysRepository.deleteByPracticeScheduleSpId(spId);

        if (request.getHolidays() != null && !request.getHolidays().isEmpty()) {
            createHolidays(spId, request.getHolidays());
        }
    }
}
