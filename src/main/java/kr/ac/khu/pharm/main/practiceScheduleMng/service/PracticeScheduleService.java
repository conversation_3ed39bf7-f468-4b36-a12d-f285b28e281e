package kr.ac.khu.pharm.main.practiceScheduleMng.service;

import jakarta.validation.Valid;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.HolidayRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.PracticeScheduleInsertRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.PracticeScheduleListRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.PracticeScheduleUpdateRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.response.HolidayResponse;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.response.PracticeScheduleListResponse;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.response.PracticeScheduleResponse;
import kr.ac.khu.pharm.main.practiceScheduleMng.entity.PracticeHolidays;
import kr.ac.khu.pharm.main.practiceScheduleMng.entity.PracticeSchedule;
import kr.ac.khu.pharm.main.practiceScheduleMng.repository.PracticeHolidaysRepository;
import kr.ac.khu.pharm.main.practiceScheduleMng.repository.PracticeScheduleRepository;
import kr.ac.khu.pharm.main.user.entity.User;
import kr.ac.khu.pharm.main.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class PracticeScheduleService {
    
    private final PracticeScheduleRepository practiceScheduleRepository;
    private final PracticeHolidaysRepository practiceHolidaysRepository;
    private final UserService userService;

    public List<PracticeScheduleListResponse> list() {
        String currentUserId = getCurrentUserId();
        List<PracticeSchedule> schedules = practiceScheduleRepository.findActiveSchedulesByUserId(currentUserId);
        return schedules.stream()
                .map(this::mapToListResponse)
                .collect(Collectors.toList());
    }

    public PracticeScheduleResponse findById(Integer spId) {
        String currentUserId = getCurrentUserId();

        Optional<PracticeSchedule> scheduleOptional =
            practiceScheduleRepository.findBySpIdAndUserIdAndDelAt(spId, currentUserId, "N");

        if (scheduleOptional.isEmpty()) {
            throw new RuntimeException("Practice schedule not found with ID: " + spId);
        }

        PracticeSchedule schedule = scheduleOptional.get();
        return mapToDetailResponse(schedule);
    }

    @Transactional
    public PracticeScheduleResponse insert(@Valid PracticeScheduleInsertRequest request) {
        String currentUserId = getCurrentUserId();
        User currentUser = userService.getByUsername(currentUserId);

        if (request.getStartDate().isAfter(request.getEndDate())) {
            throw new RuntimeException("Start date must be before end date");
        }

        // Create practice schedule entity
        PracticeSchedule schedule = new PracticeSchedule();
        schedule.setUserSn(currentUser.getId());
        schedule.setUserId(currentUserId);
        schedule.setTeam(request.getTeam());
        schedule.setDegree(request.getDegree());
        schedule.setStartDate(request.getStartDate());
        schedule.setEndDate(request.getEndDate());
        schedule.setDaysOfWeek(request.getDaysOfWeek());
        schedule.setDelAt("N");
        schedule.setRegDate(Instant.now());
        schedule.setRegUserId(currentUserId);

        // Save practice schedule
        PracticeSchedule savedSchedule = practiceScheduleRepository.save(schedule);

        // Create holidays if provided
        if (request.getHolidays() != null && !request.getHolidays().isEmpty()) {
            createHolidays(savedSchedule.getSpId(), request.getHolidays());
        }

        return findById(savedSchedule.getSpId());
    }

    /**
     * Update an existing practice schedule
     */
    @Transactional
    public PracticeScheduleResponse update(Integer spId, @Valid PracticeScheduleUpdateRequest request) {
        String currentUserId = getCurrentUserId();

        Optional<PracticeSchedule> scheduleOptional =
            practiceScheduleRepository.findBySpIdAndUserIdAndDelAt(spId, currentUserId, "N");

        if (scheduleOptional.isEmpty()) {
            throw new RuntimeException("Practice schedule not found with ID: " + spId);
        }

        PracticeSchedule schedule = scheduleOptional.get();

        // Update fields if provided
        if (request.getTeam() != null) {
            schedule.setTeam(request.getTeam());
        }
        if (request.getDegree() != null) {
            schedule.setDegree(request.getDegree());
        }
        if (request.getStartDate() != null) {
            schedule.setStartDate(request.getStartDate());
        }
        if (request.getEndDate() != null) {
            schedule.setEndDate(request.getEndDate());
        }
        if (request.getDaysOfWeek() != null) {
            schedule.setDaysOfWeek(request.getDaysOfWeek());
        }

        // Validate date range if both dates are provided
        if (schedule.getStartDate().isAfter(schedule.getEndDate())) {
            throw new RuntimeException("Start date must be before end date");
        }

        schedule.setModDate(Instant.now());
        schedule.setModUserId(currentUserId);

        // Save updated schedule
        practiceScheduleRepository.save(schedule);

        // Handle holiday updates
        updateHolidays(spId, request);

        return findById(spId);
    }

    @Transactional
    public void delete(Integer spId) {
        String currentUserId = getCurrentUserId();
        Optional<PracticeSchedule> scheduleOptional =
            practiceScheduleRepository.findBySpIdAndUserIdAndDelAt(spId, currentUserId, "N");

        if (scheduleOptional.isEmpty()) {
            throw new RuntimeException("Practice schedule not found with ID: " + spId);
        }

        PracticeSchedule schedule = scheduleOptional.get();
        schedule.setDelAt("Y");
        schedule.setModDate(Instant.now());
        schedule.setModUserId(currentUserId);

        practiceScheduleRepository.save(schedule);
    }

    private String getCurrentUserId() {
        return SecurityContextHolder.getContext().getAuthentication().getName();
    }

    private void createHolidays(Integer spId, List<HolidayRequest> holidayRequests) {
        for (HolidayRequest holidayRequest : holidayRequests) {
            PracticeHolidays holiday = new PracticeHolidays();

            // Set the practice schedule reference
            PracticeSchedule practiceSchedule = new PracticeSchedule();
            practiceSchedule.setSpId(spId);
            holiday.setPracticeSchedule(practiceSchedule);

            holiday.setHolidayDate(holidayRequest.getHolidayDate());
            holiday.setHolidayReason(holidayRequest.getHolidayReason());

            practiceHolidaysRepository.save(holiday);
        }
    }

    private void updateHolidays(Integer spId, PracticeScheduleUpdateRequest request) {

        practiceHolidaysRepository.deleteByPracticeScheduleSpId(spId);

        if (request.getHolidays() != null && !request.getHolidays().isEmpty()) {
            for (HolidayRequest holidayRequest : request.getHolidays()) {
                PracticeHolidays holiday = new PracticeHolidays();

                PracticeSchedule practiceSchedule = new PracticeSchedule();
                practiceSchedule.setSpId(spId);
                holiday.setPracticeSchedule(practiceSchedule);

                holiday.setHolidayDate(holidayRequest.getHolidayDate());
                holiday.setHolidayReason(holidayRequest.getHolidayReason());

                practiceHolidaysRepository.save(holiday);
            }
        }
    }

    /**
     * Maps a StudentPracticeSchedule entity to a PracticeScheduleListResponse DTO
     */
    private PracticeScheduleListResponse mapToListResponse(PracticeSchedule schedule) {
        PracticeScheduleListResponse response = new PracticeScheduleListResponse();
        response.setSpId(schedule.getSpId());
        response.setTeam(schedule.getTeam());
        response.setDegree(schedule.getDegree());
        response.setStartDate(schedule.getStartDate());
        response.setEndDate(schedule.getEndDate());
        response.setDaysOfWeek(schedule.getDaysOfWeek());
        response.setRegDate(schedule.getRegDate());

        // Fetch and map holidays
        List<HolidayResponse> holidayResponses = fetchAndMapHolidays(schedule.getSpId());
        response.setHolidays(holidayResponses);

        return response;
    }

    /**
     * Maps a StudentPracticeSchedule entity to a PracticeScheduleResponse DTO
     */
    private PracticeScheduleResponse mapToDetailResponse(PracticeSchedule schedule) {
        PracticeScheduleResponse response = new PracticeScheduleResponse();
        response.setSpId(schedule.getSpId());
        response.setUserSn(schedule.getUserSn());
        response.setUserId(schedule.getUserId());
        response.setTeam(schedule.getTeam());
        response.setDegree(schedule.getDegree());
        response.setStartDate(schedule.getStartDate());
        response.setEndDate(schedule.getEndDate());
        response.setDaysOfWeek(schedule.getDaysOfWeek());
        response.setDelAt(schedule.getDelAt());
        response.setRegDate(schedule.getRegDate());
        response.setModDate(schedule.getModDate());
        response.setRegUserId(schedule.getRegUserId());
        response.setModUserId(schedule.getModUserId());

        // Fetch and map holidays
        List<HolidayResponse> holidayResponses = fetchAndMapHolidays(schedule.getSpId());
        response.setHolidays(holidayResponses);

        return response;
    }

    /**
     * Fetches holidays for a given spId and maps them to HolidayResponse list
     */
    private List<HolidayResponse> fetchAndMapHolidays(Integer spId) {
        List<PracticeHolidays> holidays = practiceHolidaysRepository.findByPracticeScheduleSpId(spId);

        if (holidays.isEmpty()) {
            return new ArrayList<>();
        }

        List<HolidayResponse> holidayResponses = mapHolidaysToDtoList(holidays, spId);

        return holidayResponses;
    }

    /**
     * Maps a list of PracticeHolidays entities to a list of HolidayResponse objects
     */
    private List<HolidayResponse> mapHolidaysToDtoList(List<PracticeHolidays> holidays, Integer spId) {
        return holidays.stream()
                .map(holiday -> mapToHolidayResponse(holiday, spId))
                .collect(Collectors.toList());
    }

    private HolidayResponse mapToHolidayResponse(PracticeHolidays holiday, Integer spId) {
        HolidayResponse holidayResponse = new HolidayResponse();
        holidayResponse.setHolidayId(holiday.getHolidayId());
        holidayResponse.setSpId(spId);
        holidayResponse.setHolidayDate(holiday.getHolidayDate());
        holidayResponse.setHolidayReason(holiday.getHolidayReason());
        return holidayResponse;
    }
}
