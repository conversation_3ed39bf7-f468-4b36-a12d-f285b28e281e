package kr.ac.khu.pharm.main.auth.service;

import kr.ac.khu.pharm.main.auth.PasswordResetRepository;
import kr.ac.khu.pharm.main.auth.TokenResetException;
import kr.ac.khu.pharm.main.auth.entity.PasswordResetEntity;
import kr.ac.khu.pharm.main.user.entity.User;
import kr.ac.khu.pharm.main.user.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class PasswordResetService {
    private final PasswordResetRepository passwordResetRepository;
    private final UserService userService;

    @Value("${application.security.password-reset-token-expiration}")
    private long passwordTokenExpiration;

    @Transactional
    public String createRequest(User user) {
        Instant now = Instant.now();
        Instant oneHourAgo = Instant.now().minus(1, ChronoUnit.HOURS);
        List<PasswordResetEntity> recentAttempts = passwordResetRepository.findByUserAndRegDateAfter(user, oneHourAgo);
        int MAX_RESET_ATTEMPTS = 5;
        if (recentAttempts.size() >= MAX_RESET_ATTEMPTS) {
            throw new TokenResetException("Exceeded maximum number of attempts. Please try again later.");
        }
        passwordResetRepository.updateIsActive(user);
        PasswordResetEntity passwordResetEntity = new PasswordResetEntity();
        passwordResetEntity.setUser(user);
        passwordResetEntity.setRegDate(now);
        passwordResetEntity.setExpiryDate(now.plusMillis(passwordTokenExpiration));
        passwordResetEntity.setToken(UUID.randomUUID().toString());
        passwordResetEntity.setActive(true);
        return passwordResetRepository.save(passwordResetEntity).getToken();
    }

    @Transactional
    public User resetPassword(String token, String password) {
        PasswordResetEntity passwordReset = passwordResetRepository.findByToken(token).orElseThrow(() -> new TokenResetException("Token not found."));
        if (passwordReset.getExpiryDate().isBefore(Instant.now())) {
            throw new TokenResetException("Token is expired.");
        }
        if (!passwordReset.isActive()) {
            throw new TokenResetException("Token is expired.");
        }
        User user = userService.setPassword(passwordReset.getUser(), password);
        passwordResetRepository.deleteByUser(user);
        return user;

    }
}
