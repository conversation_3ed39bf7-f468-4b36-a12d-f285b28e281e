package kr.ac.khu.pharm.main.user.service;

import kr.ac.khu.pharm.controller.auth.dto.registration.UserRegisterRequest;
import kr.ac.khu.pharm.controller.user.dto.UserSearchRequest;
import kr.ac.khu.pharm.main.user.entity.Role;
import kr.ac.khu.pharm.main.user.entity.User;
import kr.ac.khu.pharm.main.user.repository.RoleRepository;
import kr.ac.khu.pharm.main.user.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class UserService {
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final RoleRepository roleRepository;
    private final static int MAX_LOGIN_ATTEMPTS = 5;

    public User register(UserRegisterRequest userRegisterRequest) {
        User user = new User();
        user.setUserId(userRegisterRequest.getUsername());
        user.setPassword(passwordEncoder.encode(userRegisterRequest.getPassword()));
        Role role = roleRepository.findByName(userRegisterRequest.getRole());
        if (role == null) {
            role = roleRepository.findByName("STUDENT");
        }
        user.setRole(role);
        return userRepository.save(user);
    }

    public User getByUsername(String username) {
        return userRepository.findByUserId(username);
    }

    public void handleAuthenticationSuccess(String username) {
        User user = getByUsername(username);
        user.setFailedAttempts(0);
        user.setLocked(false);
        userRepository.save(user);
    }

    public void handleAuthenticationFailure(String username) {
        User user = getByUsername(username);
        if (user == null) {
            return;
        }
        user.setFailedAttempts(user.getFailedAttempts() + 1);
        if (user.getFailedAttempts() >= MAX_LOGIN_ATTEMPTS) {
            user.setLocked(true);
        }
        userRepository.save(user);
    }

    public Optional<User> getByUsernameAndRoleAndOrgName(UserSearchRequest userSearchRequest) {
        Role role = roleRepository.findByName(userSearchRequest.getRole());
        return userRepository.findByUserIdAndRoleAndOrgNameAndIsVerified(userSearchRequest.getUsername(), role, userSearchRequest.getOrgName(), false);
    }

    public User setPassword(User user, String passwordReset) {
        user.setPassword(passwordEncoder.encode(passwordReset));
        return userRepository.save(user);
    }
}
