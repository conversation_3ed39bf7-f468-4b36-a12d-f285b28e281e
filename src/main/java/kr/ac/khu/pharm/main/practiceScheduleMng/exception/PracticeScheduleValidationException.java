package kr.ac.khu.pharm.main.practiceScheduleMng.exception;

public class PracticeScheduleValidationException extends RuntimeException {
    
    public PracticeScheduleValidationException(String message) {
        super(message);
    }
    
    public PracticeScheduleValidationException(String message, Throwable cause) {
        super(message, cause);
    }
    
    // Specific validation methods
    public static PracticeScheduleValidationException invalidDateRange() {
        return new PracticeScheduleValidationException("Start date must be before end date");
    }
    
//    public static PracticeScheduleValidationException invalidTeam(String team) {
//        return new PracticeScheduleValidationException("Invalid team: " + team + ". Team must be A, B, or C");
//    }
//
//    public static PracticeScheduleValidationException invalidDaysOfWeek(String daysOfWeek) {
//        return new PracticeScheduleValidationException("Invalid days of week format: " + daysOfWeek + ". Must be comma-separated numbers (1-7)");
//    }
}
