package kr.ac.khu.pharm.main.auth.entity;


import jakarta.persistence.*;
import kr.ac.khu.pharm.main.common.entity.BaseModel;
import kr.ac.khu.pharm.main.user.entity.User;
import lombok.*;

import java.time.Instant;

@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TC_REFRESH_TOKENS")
@NoArgsConstructor
@Data
@Builder
@AllArgsConstructor
public class RefreshToken extends BaseModel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TOKEN_SN")
    private Long id;

    @Column(nullable = false, unique = true, name = "TOKEN")
    private String refreshToken;

    @Column(nullable = false)
    private Instant expiryDate;

    @OneToOne
    @JoinColumn(name = "USER_SN", referencedColumnName = "USER_SN")
    private User user;
}
