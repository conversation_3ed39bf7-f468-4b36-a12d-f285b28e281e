package kr.ac.khu.pharm;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import kr.ac.khu.pharm.main.user.entity.UserProfile;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(name = "tc_professor_profile")
public class ProfessorProfile extends UserProfile {
    @Id
    @Column(name = "profile_sn", nullable = false)
    private Long profileSn;

    @NotNull
    @Column(name = "user_sn", nullable = false)
    private Integer userSn;

    @Size(max = 100)
    @Column(name = "prof_nm", length = 100)
    private String profNm;

    @Size(max = 32)
    @Column(name = "emp_no", length = 32)
    private String empNo;

    @Size(max = 32)
    @Column(name = "phone", length = 32)
    private String phone;

    @Size(max = 64)
    @Column(name = "email", length = 64)
    private String email;

    @Size(max = 256)
    @Column(name = "admin_memo", length = 256)
    private String adminMemo;

    @Column(name = "reg_date")
    private Instant regDate;

    @Column(name = "mod_date")
    private Instant modDate;

    @Size(max = 32)
    @Column(name = "reg_user_id", length = 32)
    private String regUserId;

    @Size(max = 32)
    @Column(name = "mod_user_id", length = 32)
    private String modUserId;

    @Size(max = 100)
    @Column(name = "reg_user_nm", length = 100)
    private String regUserNm;

}