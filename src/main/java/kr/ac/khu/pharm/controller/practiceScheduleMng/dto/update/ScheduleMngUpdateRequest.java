package kr.ac.khu.pharm.controller.practiceScheduleMng.dto.update;

import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScheduleMngUpdateRequest {
    
    private String team;
    
    private Integer degree;
    
    private LocalDate startDate;
    
    private LocalDate endDate;
    
    @Pattern(regexp = "^[1-7](,[1-7])*$", message = "Days of week must be comma-separated numbers (1-7)")
    private String daysOfWeek;
    
    // Holidays to add/update
    private List<HolidayUpdateDto> holidays;
    
    // Holiday IDs to delete
    private List<Integer> holidaysToDelete;
    
    // Current user ID for audit
    private String currentUserId;
}
