package kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HolidayRequest {

    private Integer holidayId; // null for new holidays

    @NotNull
    private LocalDate holidayDate;
    
    @Size(max = 128)
    private String holidayReason;
}
