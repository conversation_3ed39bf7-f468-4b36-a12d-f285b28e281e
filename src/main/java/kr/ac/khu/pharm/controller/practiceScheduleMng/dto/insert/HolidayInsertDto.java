package kr.ac.khu.pharm.controller.practiceScheduleMng.dto.insert;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HolidayInsertDto {
    
    @NotNull(message = "Holiday date is required")
    private LocalDate holidayDate;
    
    private String holidayReason;
}
