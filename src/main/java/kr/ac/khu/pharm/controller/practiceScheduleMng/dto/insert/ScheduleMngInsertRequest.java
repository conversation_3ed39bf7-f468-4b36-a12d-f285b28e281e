package kr.ac.khu.pharm.controller.practiceScheduleMng.dto.insert;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScheduleMngInsertRequest {
    
    @NotNull(message = "User SN is required")
    private Integer userSn;
    
    @NotBlank(message = "User ID is required")
    private String userId;
    
    @NotBlank(message = "Team is required")
    @Pattern(regexp = "^[ABC]$", message = "Team must be A, B, or C")
    private String team;
    
    @NotNull(message = "Degree is required")
    private Integer degree;
    
    @NotNull(message = "Start date is required")
    private LocalDate startDate;
    
    @NotNull(message = "End date is required")
    private LocalDate endDate;
    
    @NotBlank(message = "Days of week is required")
    @Pattern(regexp = "^[1-7](,[1-7])*$", message = "Days of week must be comma-separated numbers (1-7)")
    private String daysOfWeek;
    
    // Optional holiday dates
    private List<HolidayInsertDto> holidays;
}
