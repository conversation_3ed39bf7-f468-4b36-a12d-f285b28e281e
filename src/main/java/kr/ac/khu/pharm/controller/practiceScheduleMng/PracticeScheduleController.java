package kr.ac.khu.pharm.controller.practiceScheduleMng;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.PracticeScheduleInsertRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.PracticeScheduleUpdateRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.response.HolidayResponse;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.response.PracticeScheduleListResponse;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.response.PracticeScheduleResponse;
import kr.ac.khu.pharm.main.practiceScheduleMng.entity.PracticeHolidays;
import kr.ac.khu.pharm.main.practiceScheduleMng.entity.PracticeSchedule;
import kr.ac.khu.pharm.main.practiceScheduleMng.service.PracticeScheduleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Tag(name = "Student Practice Schedule Management", description = "학생 실습 일정 관리 API")
@RestController
@RequestMapping("/settings/practice-schedule")
@RequiredArgsConstructor
public class PracticeScheduleController {

    private final PracticeScheduleService practiceScheduleService;

    @Operation(summary = "Get list of practice schedules", description = "View the current user's practice schedule list.")
    @PostMapping("/list")
    public ResponseEntity<List<PracticeScheduleListResponse>> list() {
        List<PracticeSchedule> entityList = practiceScheduleService.list();

        List<PracticeScheduleListResponse> scheduleList = new ArrayList<>();
        for( PracticeSchedule schedule : entityList) {
            PracticeScheduleListResponse scheduleItem = new PracticeScheduleListResponse();
            scheduleItem.setSpId(schedule.getSpId());
            scheduleItem.setTeam(schedule.getTeam());
            scheduleItem.setDegree(schedule.getDegree());
            scheduleItem.setStartDate(schedule.getStartDate());
            scheduleItem.setEndDate(schedule.getEndDate());
            scheduleItem.setDaysOfWeek(schedule.getDaysOfWeek());
            scheduleItem.setDelAt(schedule.getDelAt());
            scheduleItem.setRegDate(schedule.getRegDate());

            List <PracticeHolidays> holidays = practiceScheduleService.holidayList(schedule.getSpId());
            // map holidays
            if(!holidays.isEmpty()) {
                scheduleItem.setHolidays(mapToHolidayResponse(holidays, schedule.getSpId()));
            } else {
                scheduleItem.setHolidays(null);
            }
            scheduleList.add(scheduleItem);
        }

        return ResponseEntity.ok(scheduleList);
    }

    @Operation(summary = "View detailed practice schedule", description = "View details of practice schedule.")
    @GetMapping("/{spId}")
    public ResponseEntity<PracticeScheduleResponse> view(@PathVariable Integer spId) {
        PracticeScheduleResponse response = dtoResponse(practiceScheduleService.findById(spId));
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Insert a practice schedule", description = "Create a new practice schedule.")
    @PostMapping("/insert")
    public ResponseEntity<PracticeScheduleResponse> insert(@Valid @RequestBody PracticeScheduleInsertRequest request) {
        PracticeScheduleResponse response = dtoResponse(practiceScheduleService.insert(request));
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(summary = "Update a practice schedule", description = "Modify the existing practice schedule.")
    @PutMapping("/{spId}")
    public ResponseEntity<PracticeScheduleResponse> update(@PathVariable Integer spId, @Valid @RequestBody PracticeScheduleUpdateRequest request) {
        PracticeScheduleResponse response = dtoResponse(practiceScheduleService.update(spId, request));
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Delete the practice schedule", description = "Delete the practice schedule (soft delete).")
    @DeleteMapping("/{spId}")
    public ResponseEntity<Void> delete(@PathVariable Integer spId) {
        practiceScheduleService.delete(spId);
        return ResponseEntity.noContent().build();
    }

    /**
     * Maps a StudentPracticeSchedule entity to a PracticeScheduleResponse DTO
     */
    private PracticeScheduleResponse dtoResponse(PracticeSchedule schedule) {
        PracticeScheduleResponse response = new PracticeScheduleResponse();
        response.setSpId(schedule.getSpId());
        response.setTeam(schedule.getTeam());
        response.setDegree(schedule.getDegree());
        response.setStartDate(schedule.getStartDate());
        response.setEndDate(schedule.getEndDate());
        response.setDaysOfWeek(schedule.getDaysOfWeek());
        response.setDelAt(schedule.getDelAt());
        response.setRegDate(schedule.getRegDate());
        response.setModDate(schedule.getModDate());
        response.setRegUserId(schedule.getRegUserId());
        response.setModUserId(schedule.getModUserId());

        // Fetch and map holidays
        List <PracticeHolidays> holidays = practiceScheduleService.holidayList(schedule.getSpId());
        // Fetch and map holidays
        if(!holidays.isEmpty()) {
            response.setHolidays(mapToHolidayResponse(holidays, schedule.getSpId()));
        } else {
            response.setHolidays(null);
        }
        return response;
    }


    /**
     * Maps a list of PracticeHolidays entities to a list of HolidayResponse objects
     */

    private List<HolidayResponse> mapToHolidayResponse(List<PracticeHolidays> holidays, Integer spId) {
        List<HolidayResponse> holidayResponses = new ArrayList<>();
        for (PracticeHolidays holiday : holidays) {
            HolidayResponse holidayResponse = new HolidayResponse();
            holidayResponse.setHolidayId(holiday.getHolidayId());
            holidayResponse.setSpId(spId);
            holidayResponse.setHolidayDate(holiday.getHolidayDate());
            holidayResponse.setHolidayReason(holiday.getHolidayReason());
            holidayResponses.add(holidayResponse);
        }
        return holidayResponses;
    }
}