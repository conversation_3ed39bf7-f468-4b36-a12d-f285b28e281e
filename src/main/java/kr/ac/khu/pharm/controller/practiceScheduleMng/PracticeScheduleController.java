package kr.ac.khu.pharm.controller.practiceScheduleMng;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.PracticeScheduleInsertRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.PracticeScheduleUpdateRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.response.PracticeScheduleListResponse;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.response.PracticeScheduleResponse;
import kr.ac.khu.pharm.main.practiceScheduleMng.service.PracticeScheduleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Tag(name = "Student Practice Schedule Management", description = "학생 실습 일정 관리 API")
@RestController
@RequestMapping("/user/practice-schedule")
@RequiredArgsConstructor
public class PracticeScheduleController {
    
    private final PracticeScheduleService practiceScheduleService;

    @Operation(summary = "Get list of practice schedules", description = "View the current user's practice schedule list.")
    @PostMapping("/list")
    public ResponseEntity<List<PracticeScheduleListResponse>> list() {
        try {
            List<PracticeScheduleListResponse> response = practiceScheduleService.list();
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error fetching practice schedule list: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "View detailed practice schedule", description = "View details of practice schedule.")
    @GetMapping("/{spId}")
    public ResponseEntity<PracticeScheduleResponse> view(@PathVariable Integer spId) {
        log.info("Received request to view practice schedule with spId: {}", spId);

        PracticeScheduleResponse response = practiceScheduleService.findById(spId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Insert a practice schedule", description = "Create a new practice schedule.")
    @PostMapping("/insert")
    public ResponseEntity<PracticeScheduleResponse> insert(@Valid @RequestBody PracticeScheduleInsertRequest request) {
        log.info("Received request to create practice schedule");

        PracticeScheduleResponse response = practiceScheduleService.insert(request);
        log.info("Created practice schedule with spId: {}", response.getSpId());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(summary = "Update a practice schedule", description = "Modify the existing practice schedule.")
    @PutMapping("/{spId}")
    public ResponseEntity<PracticeScheduleResponse> update(
            @PathVariable Integer spId,
            @Valid @RequestBody PracticeScheduleUpdateRequest request) {
        log.info("Received request to update practice schedule with spId: {}", spId);

        PracticeScheduleResponse response = practiceScheduleService.update(spId, request);
        log.info("Updated practice schedule with spId: {}", spId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Delete the practice schedule", description = "Delete the practice schedule (soft delete).")
    @DeleteMapping("/{spId}")
    public ResponseEntity<Void> delete(@PathVariable Integer spId) {
        log.info("Received request to delete practice schedule with spId: {}", spId);

        practiceScheduleService.delete(spId);
        log.info("Deleted practice schedule with spId: {}", spId);
        return ResponseEntity.noContent().build();
    }
}