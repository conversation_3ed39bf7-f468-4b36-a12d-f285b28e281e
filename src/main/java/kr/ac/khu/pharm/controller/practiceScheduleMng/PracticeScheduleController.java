package kr.ac.khu.pharm.controller.practiceScheduleMng;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.PracticeScheduleInsertRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request.PracticeScheduleUpdateRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.response.PracticeScheduleListResponse;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.response.PracticeScheduleResponse;
import kr.ac.khu.pharm.main.practiceScheduleMng.service.PracticeScheduleService;
import kr.ac.khu.pharm.main.practiceScheduleMng.exception.PracticeScheduleNotFoundException;
import kr.ac.khu.pharm.main.practiceScheduleMng.exception.PracticeScheduleValidationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Tag(name = "Student Practice Schedule Management", description = "학생 실습 일정 관리 API")
@RestController
@RequestMapping("/user/practice-schedule")
@RequiredArgsConstructor
public class PracticeScheduleController {

    private final PracticeScheduleService practiceScheduleService;

    @Operation(summary = "Get list of practice schedules", description = "View the current user's practice schedule list.")
    @PostMapping("/list")
    public ResponseEntity<List<PracticeScheduleListResponse>> list() {
        try {
            List<PracticeScheduleListResponse> response = practiceScheduleService.list();
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error fetching practice schedule list: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "View detailed practice schedule", description = "View details of practice schedule.")
    @GetMapping("/{spId}")
    public ResponseEntity<PracticeScheduleResponse> view(@PathVariable Integer spId) {
        try {
            PracticeScheduleResponse response = practiceScheduleService.findById(spId);
            return ResponseEntity.ok(response);
        } catch (PracticeScheduleNotFoundException e) {
            log.error("Practice schedule not found: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        } catch (Exception e) {
            log.error("Unexpected error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Insert a practice schedule", description = "Create a new practice schedule.")
    @PostMapping("/insert")
    public ResponseEntity<PracticeScheduleResponse> insert(@Valid @RequestBody PracticeScheduleInsertRequest request) {
        try {
            PracticeScheduleResponse response = practiceScheduleService.insert(request);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (PracticeScheduleValidationException e) {
            log.error("Validation error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        } catch (Exception e) {
            log.error("Unexpected error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Update a practice schedule", description = "Modify the existing practice schedule.")
    @PutMapping("/{spId}")
    public ResponseEntity<PracticeScheduleResponse> update(
            @PathVariable Integer spId,
            @Valid @RequestBody PracticeScheduleUpdateRequest request) {
        try {
            PracticeScheduleResponse response = practiceScheduleService.update(spId, request);
            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            log.error("Error updating practice schedule: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        } catch (Exception e) {
            log.error("Unexpected error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Delete the practice schedule", description = "Delete the practice schedule (soft delete).")
    @DeleteMapping("/{spId}")
    public ResponseEntity<Void> delete(@PathVariable Integer spId) {
        try {
            practiceScheduleService.delete(spId);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            log.error("Error deleting practice schedule: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        } catch (Exception e) {
            log.error("Unexpected error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}