package kr.ac.khu.pharm.controller.practiceScheduleMng.dto.update;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScheduleMngUpdateResponse {
    
    private Integer spId;
    private String team;
    private Integer degree;
    private LocalDate startDate;
    private LocalDate endDate;
    private String daysOfWeek;
    private String message;
}
