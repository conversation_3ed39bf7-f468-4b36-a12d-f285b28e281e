package kr.ac.khu.pharm.controller.practiceScheduleMng.exception;

import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.response.ErrorResponse;
import kr.ac.khu.pharm.main.practiceScheduleMng.exception.PracticeScheduleAccessDeniedException;
import kr.ac.khu.pharm.main.practiceScheduleMng.exception.PracticeScheduleNotFoundException;
import kr.ac.khu.pharm.main.practiceScheduleMng.exception.PracticeScheduleValidationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestControllerAdvice(basePackages = "kr.ac.khu.pharm.controller.practiceScheduleMng")
public class PracticeScheduleExceptionHandler {

    @ExceptionHandler(PracticeScheduleNotFoundException.class)
    public ResponseEntity<ErrorResponse> handlePracticeScheduleNotFoundException(
            PracticeScheduleNotFoundException ex, WebRequest request) {
        
        log.error("Practice schedule not found: {}", ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
                "PRACTICE_SCHEDULE_NOT_FOUND",
                ex.getMessage(),
                HttpStatus.NOT_FOUND.value(),
                request.getDescription(false).replace("uri=", "")
        );
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    @ExceptionHandler(PracticeScheduleValidationException.class)
    public ResponseEntity<ErrorResponse> handlePracticeScheduleValidationException(
            PracticeScheduleValidationException ex, WebRequest request) {
        
        log.error("Practice schedule validation error: {}", ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
                "PRACTICE_SCHEDULE_VALIDATION_ERROR",
                ex.getMessage(),
                HttpStatus.BAD_REQUEST.value(),
                request.getDescription(false).replace("uri=", "")
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    @ExceptionHandler(PracticeScheduleAccessDeniedException.class)
    public ResponseEntity<ErrorResponse> handlePracticeScheduleAccessDeniedException(
            PracticeScheduleAccessDeniedException ex, WebRequest request) {
        
        log.error("Practice schedule access denied: {}", ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
                "PRACTICE_SCHEDULE_ACCESS_DENIED",
                ex.getMessage(),
                HttpStatus.FORBIDDEN.value(),
                request.getDescription(false).replace("uri=", "")
        );
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationExceptions(
            MethodArgumentNotValidException ex, WebRequest request) {

        log.error("Validation error: {}", ex.getMessage());

        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        String message = "Validation failed: " + errors.toString();

        ErrorResponse errorResponse = new ErrorResponse(
                "VALIDATION_ERROR",
                message,
                HttpStatus.BAD_REQUEST.value(),
                request.getDescription(false).replace("uri=", "")
        );

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(
            Exception ex, WebRequest request) {

        log.error("Unexpected error: ", ex);

        ErrorResponse errorResponse = new ErrorResponse(
                "INTERNAL_SERVER_ERROR",
                "An unexpected error occurred. Please try again later.",
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                request.getDescription(false).replace("uri=", "")
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
}
