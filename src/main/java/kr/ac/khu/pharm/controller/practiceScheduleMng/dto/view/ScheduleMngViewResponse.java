package kr.ac.khu.pharm.controller.practiceScheduleMng.dto.view;

import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.list.HolidayDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ScheduleMngViewResponse {
    private Integer spId;
    private Integer userSn;
    private String userId;
    private String team;
    private Integer degree;
    private LocalDate startDate;
    private LocalDate endDate;
    private String daysOfWeek;
    private String delAt;
    private String regUserId;

    // Holiday list
    private List<HolidayDto> holiday;
}
