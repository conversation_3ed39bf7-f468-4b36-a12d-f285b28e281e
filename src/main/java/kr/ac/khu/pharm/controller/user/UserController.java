package kr.ac.khu.pharm.controller.user;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import kr.ac.khu.pharm.controller.user.dto.UserSearchRequest;
import kr.ac.khu.pharm.controller.user.dto.UserSearchResponse;
import kr.ac.khu.pharm.main.user.entity.User;
import kr.ac.khu.pharm.main.user.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@Tag(name = "User services", description = "Services related to user manipulation")
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {
    private final UserService userService;

    @Operation(
            summary = "Search for a user",
            description = "Search for a user based on username, user type and organization"
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "User found",
                    content = @Content(schema = @Schema(implementation = UserSearchResponse.class))
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "User not found",
                    content = @Content(schema = @Schema(implementation = UserSearchResponse.class))
            )
    })
    @PostMapping("/search")
    public ResponseEntity<UserSearchResponse> searchUser(@Valid @RequestBody UserSearchRequest userSearchRequest) {
        Optional<User> optionalUser = userService.getByUsernameAndRoleAndOrgName(userSearchRequest);
        UserSearchResponse searchResponse = new UserSearchResponse();
        if (optionalUser.isPresent()) {
            User user = optionalUser.get();
            searchResponse.setUsername(user.getUserId());
            searchResponse.setMessage("success");
            return ResponseEntity.ok(searchResponse);
        }
        searchResponse.setMessage("User not found");
        return new ResponseEntity<>(searchResponse, HttpStatus.NOT_FOUND);
    }
}
