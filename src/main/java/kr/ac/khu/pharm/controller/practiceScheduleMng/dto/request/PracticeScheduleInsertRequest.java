package kr.ac.khu.pharm.controller.practiceScheduleMng.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PracticeScheduleInsertRequest {
    
    @NotNull
    private String team;
    
    @NotNull
    private Integer degree;
    
    @NotNull
    private LocalDate startDate;
    
    @NotNull
    private LocalDate endDate;
    
    @NotNull
    @Pattern(regexp = "^[1-7](,[1-7])*$", message = "Days of week must be comma-separated numbers (1-7)")
    private String daysOfWeek;
    
    @Valid
    private List<HolidayRequest> holidays;
}
