package kr.ac.khu.pharm.controller.auth;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import kr.ac.khu.pharm.controller.auth.dto.login.LogoutResponse;
import kr.ac.khu.pharm.controller.auth.dto.login.UserLoginRequest;
import kr.ac.khu.pharm.controller.auth.dto.login.UserLoginResponse;
import kr.ac.khu.pharm.controller.auth.dto.refresh.TokenRefreshRequest;
import kr.ac.khu.pharm.controller.auth.dto.refresh.TokenRefreshResponse;
import kr.ac.khu.pharm.controller.auth.dto.registration.UserRegisterRequest;
import kr.ac.khu.pharm.controller.auth.dto.registration.UserRegisterResponse;
import kr.ac.khu.pharm.controller.auth.dto.reset.ForgotPasswordResponse;
import kr.ac.khu.pharm.controller.auth.dto.reset.ResetPasswordRequest;
import kr.ac.khu.pharm.controller.auth.dto.reset.ResetPasswordResponse;
import kr.ac.khu.pharm.controller.user.dto.UserSearchRequest;
import kr.ac.khu.pharm.main.auth.TokenRefreshException;
import kr.ac.khu.pharm.main.auth.entity.RefreshToken;
import kr.ac.khu.pharm.main.auth.service.JwtTokenProvider;
import kr.ac.khu.pharm.main.auth.service.PasswordResetService;
import kr.ac.khu.pharm.main.auth.service.RefreshTokenService;
import kr.ac.khu.pharm.main.user.entity.CustomUserDetails;
import kr.ac.khu.pharm.main.user.entity.User;
import kr.ac.khu.pharm.main.user.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@Tag(name = "User Authentication", description = "Endpoints for user authentication")
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {
    private final UserService userService;
    private final AuthenticationManager authenticationManager;
    private final JwtTokenProvider jwtTokenProvider;
    private final RefreshTokenService refreshTokenService;
    private final PasswordResetService passwordResetService;

    @Operation(
            summary = "User login endpoint",
            description = "Get access and refresh tokens. Also returns user type. Blocks user after 5 unsuccessful attempts"
    )
    @PostMapping("/login")
    public ResponseEntity<UserLoginResponse> authenticateUser(@Valid @RequestBody UserLoginRequest loginRequest) {
        UserLoginResponse response = new UserLoginResponse();
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword())
        );

        String jwt = jwtTokenProvider.generateAccessToken(authentication);
        User user = userService.getByUsername(loginRequest.getUsername());
        RefreshToken refreshToken = refreshTokenService.createRefreshToken(Long.valueOf(user.getId()));
        response.setToken(jwt);
        response.setUsername(loginRequest.getUsername());
        response.setUserType(user.getRole().getName());
        response.setRefreshToken(refreshToken.getRefreshToken());
//        StudentProfile studentProfile = (StudentProfile) user.getProfile();  // keeping this to remind myself
        return ResponseEntity.ok(response);
    }   

    @Operation(
            summary = "User registration endpoint",
            description = "Creates new user based on input data. Does not create profile of the user"
    )
    @PostMapping("/register")
    public ResponseEntity<UserRegisterResponse> registerUser(@Valid @RequestBody UserRegisterRequest userRegisterRequest) {
        User user = userService.register(userRegisterRequest);
        UserRegisterResponse response = new UserRegisterResponse(
                user.getUserId(),
                user.getRole().getName(),
                user.getId()
        );
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Refresh authorization",
            description = "Get new access token based on refresh token. Also generates new refresh token"
    )
    @PostMapping("/refresh")
    public ResponseEntity<TokenRefreshResponse> refreshToken(@Valid @RequestBody TokenRefreshRequest refreshRequest) {
        String requestRefreshToken = refreshRequest.getRefreshToken();
        Optional<RefreshToken> optionalRefreshToken = refreshTokenService.findByRefreshToken(requestRefreshToken);
        if (!optionalRefreshToken.isPresent()) {
            throw new TokenRefreshException(requestRefreshToken, "Token not found.");
        }

        RefreshToken refreshToken = refreshTokenService.verifyRefreshTokenExpiration(optionalRefreshToken.get());

        String jwt = jwtTokenProvider.generateAccessToken(optionalRefreshToken.get().getUser());

        RefreshToken newRefreshToken = refreshTokenService.createRefreshToken(Long.valueOf(refreshToken.getUser().getUserId()));

        TokenRefreshResponse response = new TokenRefreshResponse(jwt, newRefreshToken.getRefreshToken());
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Logout user",
            description = "Deleted refresh token from database."
    )
    @PostMapping("/logout")
    public ResponseEntity<LogoutResponse> logoutUser() {
        CustomUserDetails userDetails = (CustomUserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        int userSn = userDetails.getUserSn();
        refreshTokenService.deleteRefreshTokenByUserSn((long) userSn);
        return ResponseEntity.ok(new LogoutResponse("Logout successful"));
    }

    @PostMapping("/forgot-password")
    public ResponseEntity<ForgotPasswordResponse> forgotPassword(@RequestBody UserSearchRequest userSearchRequest) {
        User user = userService.getByUsernameAndRoleAndOrgName(userSearchRequest).orElseThrow(() -> new UsernameNotFoundException("No such user"));
        ForgotPasswordResponse response = new ForgotPasswordResponse();
        String token = passwordResetService.createRequest(user);
        // todo send token ever email
        response.setMessage("A password reset link has been sent to your email");
        response.setEmail(user.getMail());
        return ResponseEntity.ok(response);
    }

    @PostMapping("/reset-password/{token}")
    public ResponseEntity<ResetPasswordResponse> resetPassword(@PathVariable String token, @RequestBody ResetPasswordRequest resetPasswordRequest) {
        ResetPasswordResponse response = new ResetPasswordResponse();
        User user = passwordResetService.resetPassword(token, resetPasswordRequest.getPassword());
        response.setUsername(user.getUserId());
        response.setMessage("Password has been reset");
        response.setEmail(token);
        // todo send notification over email
        return ResponseEntity.ok(response);
    }

}