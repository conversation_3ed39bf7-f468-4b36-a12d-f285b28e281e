package kr.ac.khu.pharm.controller.practiceScheduleMng.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PracticeScheduleListResponse {
    
    private Integer spId;
    private String team;
    private Integer degree;
    private LocalDate startDate;
    private LocalDate endDate;
    private String daysOfWeek;
    private String delAt;
    private Instant regDate;
    
    private List<HolidayResponse> holidays;
}
