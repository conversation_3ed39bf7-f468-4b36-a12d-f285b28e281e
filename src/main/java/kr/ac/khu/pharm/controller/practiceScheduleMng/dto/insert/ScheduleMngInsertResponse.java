package kr.ac.khu.pharm.controller.practiceScheduleMng.dto.insert;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScheduleMngInsertResponse {
    
    private Integer spId;
    private Integer userSn;
    private String userId;
    private String team;
    private Integer degree;
    private LocalDate startDate;
    private LocalDate endDate;
    private String daysOfWeek;
    private String message;
}
