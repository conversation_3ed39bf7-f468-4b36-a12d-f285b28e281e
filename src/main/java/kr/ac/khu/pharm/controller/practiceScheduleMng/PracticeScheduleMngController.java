package kr.ac.khu.pharm.controller.practiceScheduleMng;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.insert.ScheduleMngInsertRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.insert.ScheduleMngInsertResponse;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.list.ScheduleMngListRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.list.ScheduleMngListResponse;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.update.ScheduleMngUpdateRequest;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.update.ScheduleMngUpdateResponse;
import kr.ac.khu.pharm.controller.practiceScheduleMng.dto.view.ScheduleMngViewResponse;
import kr.ac.khu.pharm.main.practiceScheduleMng.service.PracticeScheduleMngService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Tag(name = "Practice Schedule Management", description = "학생 실습 일정 관리 API")
@RestController
@RequestMapping("/user/schedule")
@RequiredArgsConstructor
public class PracticeScheduleMngController {
    
    private final PracticeScheduleMngService practiceScheduleMngService;

    @Operation(summary = "실습 일정 목록 조회", description = "사용자의 실습 일정 목록을 조회합니다.")
    @PostMapping("/list")
    public ResponseEntity<List<ScheduleMngListResponse>> practiceScheduleList(@Valid @RequestBody ScheduleMngListRequest request) {
        log.info("Received request to list practice schedules for userId: {}", request.getUserId());
        
        try {
            List<ScheduleMngListResponse> response = practiceScheduleMngService.list(request);
            log.info("Returning {} practice schedules for userId: {}", response.size(), request.getUserId());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error fetching practice schedule list: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "실습 일정 상세 조회", description = "특정 실습 일정의 상세 정보를 조회합니다.")
    @GetMapping("/{spId}")
    public ResponseEntity<ScheduleMngViewResponse> practiceScheduleView(@PathVariable Integer spId) {
        log.info("Received request to view practice schedule with spId: {}", spId);
        
        try {
            ScheduleMngViewResponse response = practiceScheduleMngService.findBySpId(spId);
            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            log.error("Error fetching practice schedule: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        } catch (Exception e) {
            log.error("Unexpected error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "실습 일정 생성", description = "새로운 실습 일정을 생성합니다.")
    @PostMapping
    public ResponseEntity<ScheduleMngInsertResponse> createPracticeSchedule(@Valid @RequestBody ScheduleMngInsertRequest request) {
        log.info("Received request to create practice schedule for userId: {}", request.getUserId());
        
        try {
            ScheduleMngInsertResponse response = practiceScheduleMngService.createPracticeSchedule(request);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (RuntimeException e) {
            log.error("Error creating practice schedule: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        } catch (Exception e) {
            log.error("Unexpected error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "실습 일정 수정", description = "기존 실습 일정을 수정합니다.")
    @PutMapping("/{spId}")
    public ResponseEntity<ScheduleMngUpdateResponse> updatePracticeSchedule(
            @PathVariable Integer spId,
            @Valid @RequestBody ScheduleMngUpdateRequest request) {
        log.info("Received request to update practice schedule with spId: {}", spId);
        
        try {
            ScheduleMngUpdateResponse response = practiceScheduleMngService.updatePracticeSchedule(spId, request);
            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            log.error("Error updating practice schedule: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        } catch (Exception e) {
            log.error("Unexpected error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "실습 일정 삭제", description = "실습 일정을 삭제합니다 (소프트 삭제).")
    @DeleteMapping("/{spId}")
    public ResponseEntity<String> deletePracticeSchedule(
            @PathVariable Integer spId,
            @RequestParam String currentUserId) {
        log.info("Received request to delete practice schedule with spId: {} by user: {}", spId, currentUserId);
        
        try {
            practiceScheduleMngService.deletePracticeScheduleById(spId, currentUserId);
            return ResponseEntity.ok("Practice schedule deleted successfully");
        } catch (RuntimeException e) {
            log.error("Error deleting practice schedule: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Internal server error");
        }
    }
}
