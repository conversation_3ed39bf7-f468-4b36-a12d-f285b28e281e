package kr.ac.khu.pharm.controller.auth.dto.registration;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
public class UserRegisterRequest {
    @NotEmpty
    private String username;
    @NotEmpty
    private String password;
    @Email
    @NotEmpty
    private String email;
    @Pattern(regexp = "STUDENT|PRECEPTOR|PROFESSOR")
    private String role;
}
